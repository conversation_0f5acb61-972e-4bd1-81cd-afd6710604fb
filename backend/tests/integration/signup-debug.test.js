/**
 * Debug test for signup function
 * Tests the signup endpoint specifically to understand what's happening
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Base URL for the API
const API_URL = 'http://localhost:3000';

// Test data
const TEST_USER = {
  email: `test-debug-${uuidv4().substring(0, 8)}@example.com`,
  password: 'TestPassword123!',
  username: `testuser-debug-${uuidv4().substring(0, 8)}`,
  firstName: 'Test',
  lastName: 'User'
};

describe('Signup Debug Test', () => {
  it('should test signup endpoint and show detailed error information', async () => {
    console.log('Testing signup with user:', {
      email: TEST_USER.email,
      username: TEST_USER.username,
      firstName: TEST_USER.firstName,
      lastName: TEST_USER.lastName
    });

    try {
      const response = await axios.post(`${API_URL}/auth/signup`, TEST_USER);
      
      console.log('✅ Signup successful!');
      console.log('Response status:', response.status);
      console.log('Response data:', JSON.stringify(response.data, null, 2));
      
      expect(response.status).toBe(201);
      expect(response.data.message).toBe('User created successfully');
      expect(response.data.user).toBeDefined();
      expect(response.data.user.email).toBe(TEST_USER.email);
      
    } catch (error) {
      console.log('❌ Signup failed!');
      console.log('Error status:', error.response?.status);
      console.log('Error data:', JSON.stringify(error.response?.data, null, 2));
      console.log('Error message:', error.message);
      
      if (error.response?.data?.details) {
        console.log('Error details:', error.response.data.details);
      }
      
      // Don't fail the test, just log the information
      console.log('This is expected if AWS credentials are not configured');
    }
  });

  it('should test health endpoint for comparison', async () => {
    try {
      const response = await axios.get(`${API_URL}/health`);
      
      console.log('✅ Health check successful!');
      console.log('Health status:', response.status);
      console.log('Health data:', JSON.stringify(response.data, null, 2));
      
      expect(response.status).toBe(200);
      expect(response.data.status).toBe('healthy');
      
    } catch (error) {
      console.log('❌ Health check failed!');
      console.log('Error:', error.message);
      throw error;
    }
  });
});

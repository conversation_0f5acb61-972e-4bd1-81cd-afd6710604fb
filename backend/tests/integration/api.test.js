/**
 * Integration tests for the GameFlex SAM Backend API
 * These tests make real HTTP requests to the running backend
 * 
 * IMPORTANT: The backend must be running on http://localhost:3000 before running these tests
 * Start the backend with: npm start
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Base URL for the API
const API_URL = 'http://localhost:3000';

// Test data
const TEST_USER = {
  email: `test-${uuidv4().substring(0, 8)}@example.com`,
  password: 'TestPassword123!',
  username: `testuser-${uuidv4().substring(0, 8)}`,
  firstName: 'Test',
  lastName: 'User'
};

// Store tokens and IDs for use across tests
let authTokens = {};
let testUserId = '';
let testPostId = '';
let testMediaId = '';

// Helper to check if backend is running
const checkBackendRunning = async () => {
  try {
    await axios.get(`${API_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
};

// Skip all tests if backend is not running
beforeAll(async () => {
  const isRunning = await checkBackendRunning();
  if (!isRunning) {
    console.error('\n⚠️ Backend is not running at http://localhost:3000');
    console.error('Please start the backend with: npm start\n');
    // This will cause Jest to skip all tests
    throw new Error('Backend not running');
  }
  console.log('\n✅ Backend is running, proceeding with tests\n');
});

describe('GameFlex API Integration Tests', () => {
  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await axios.get(`${API_URL}/health`);
      
      expect(response.status).toBe(200);
      expect(response.data.status).toBe('healthy');
      expect(response.data.services).toBeDefined();
      expect(response.data.services.database).toBeDefined();
      expect(response.data.services.api).toBeDefined();
    });
  });

  describe('Authentication Flow', () => {
    it('should sign up a new user', async () => {
      try {
        const response = await axios.post(`${API_URL}/auth/signup`, TEST_USER);
        
        expect(response.status).toBe(201);
        expect(response.data.message).toBe('User created successfully');
        expect(response.data.user).toBeDefined();
        expect(response.data.user.email).toBe(TEST_USER.email);
        expect(response.data.user.username).toBe(TEST_USER.username);
        
        // Store user ID for later tests
        testUserId = response.data.user.id;
        
        console.log(`Created test user with ID: ${testUserId}`);
      } catch (error) {
        if (error.response) {
          console.error('Signup error:', error.response.data);
        }
        throw error;
      }
    });

    it('should sign in with the new user', async () => {
      try {
        const response = await axios.post(`${API_URL}/auth/signin`, {
          email: TEST_USER.email,
          password: TEST_USER.password
        });
        
        expect(response.status).toBe(200);
        expect(response.data.message).toBe('Sign in successful');
        expect(response.data.tokens).toBeDefined();
        expect(response.data.tokens.accessToken).toBeDefined();
        expect(response.data.tokens.refreshToken).toBeDefined();
        expect(response.data.user).toBeDefined();
        expect(response.data.user.email).toBe(TEST_USER.email);
        
        // Store tokens for later tests
        authTokens = response.data.tokens;
        
        console.log('Successfully signed in and received tokens');
      } catch (error) {
        if (error.response) {
          console.error('Signin error:', error.response.data);
        }
        throw error;
      }
    });

    it('should refresh tokens', async () => {
      // Skip if we don't have a refresh token
      if (!authTokens.refreshToken) {
        console.warn('Skipping token refresh test - no refresh token available');
        return;
      }
      
      try {
        const response = await axios.post(`${API_URL}/auth/refresh`, {
          refreshToken: authTokens.refreshToken
        });
        
        expect(response.status).toBe(200);
        expect(response.data.message).toBe('Token refreshed successfully');
        expect(response.data.tokens).toBeDefined();
        expect(response.data.tokens.accessToken).toBeDefined();
        
        // Update access token
        authTokens.accessToken = response.data.tokens.accessToken;
        
        console.log('Successfully refreshed tokens');
      } catch (error) {
        if (error.response) {
          console.error('Token refresh error:', error.response.data);
        }
        throw error;
      }
    });
  });

  describe('User Profile', () => {
    it('should get user profile', async () => {
      // Skip if we don't have a user ID
      if (!testUserId) {
        console.warn('Skipping profile test - no user ID available');
        return;
      }
      
      try {
        const response = await axios.get(`${API_URL}/users/profile?userId=${testUserId}`, {
          headers: {
            Authorization: `Bearer ${authTokens.accessToken}`
          }
        });
        
        expect(response.status).toBe(200);
        expect(response.data.user).toBeDefined();
        expect(response.data.user.id).toBe(testUserId);
        expect(response.data.user.username).toBe(TEST_USER.username);
        
        console.log('Successfully retrieved user profile');
      } catch (error) {
        if (error.response) {
          console.error('Get profile error:', error.response.data);
        }
        throw error;
      }
    });

    it('should update user profile', async () => {
      // Skip if we don't have a user ID
      if (!testUserId) {
        console.warn('Skipping profile update test - no user ID available');
        return;
      }
      
      const profileUpdate = {
        userId: testUserId,
        bio: 'This is a test bio',
        location: 'Test City',
        website: 'https://example.com'
      };
      
      try {
        const response = await axios.put(`${API_URL}/users/profile`, profileUpdate, {
          headers: {
            Authorization: `Bearer ${authTokens.accessToken}`
          }
        });
        
        expect(response.status).toBe(200);
        expect(response.data.message).toBe('Profile updated successfully');
        
        console.log('Successfully updated user profile');
      } catch (error) {
        if (error.response) {
          console.error('Update profile error:', error.response.data);
        }
        throw error;
      }
    });
  });

  // Add more test suites as needed
});

/**
 * Mock Lambda handlers for testing
 * This file provides mock implementations of the Lambda handlers
 * to avoid import errors during testing
 */

// Create a basic response helper
const createResponse = (statusCode, body) => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
  },
  body: JSON.stringify(body)
});

// Mock health handler
const healthHandler = async (event) => {
  try {
    if (event.httpMethod === 'GET' && event.path === '/health') {
      // Test DynamoDB connection (this will be mocked in tests)
      let dbStatus = 'healthy';
      try {
        const AWS = require('aws-sdk');
        const dynamodb = new AWS.DynamoDB.DocumentClient();
        await dynamodb.scan({
          TableName: process.env.USERS_TABLE || 'gameflex-test-Users',
          Limit: 1
        }).promise();
      } catch (error) {
        dbStatus = 'unhealthy';
      }

      return createResponse(200, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.ENVIRONMENT || 'test',
        version: '1.0.0',
        services: {
          database: dbStatus,
          api: 'healthy'
        },
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment_variables: {
          USER_POOL_ID: process.env.USER_POOL_ID ? 'set' : 'not set',
          USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID ? 'set' : 'not set',
          USERS_TABLE: process.env.USERS_TABLE ? 'set' : 'not set',
          POSTS_TABLE: process.env.POSTS_TABLE ? 'set' : 'not set',
          MEDIA_BUCKET: process.env.MEDIA_BUCKET ? 'set' : 'not set'
        }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
};

// Mock auth handler
const authHandler = async (event) => {
  try {
    if (event.httpMethod === 'POST' && event.path === '/auth/signup') {
      return createResponse(201, {
        message: 'User created successfully',
        user: { id: 'test-id', email: '<EMAIL>' }
      });
    } else if (event.httpMethod === 'POST' && event.path === '/auth/signin') {
      return createResponse(200, {
        message: 'Sign in successful',
        tokens: { accessToken: 'test-token', refreshToken: 'test-refresh' },
        user: { id: 'test-id', email: '<EMAIL>' }
      });
    } else if (event.httpMethod === 'POST' && event.path === '/auth/refresh') {
      return createResponse(200, {
        message: 'Token refreshed successfully',
        tokens: { accessToken: 'new-token', idToken: 'new-id-token' }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error', details: error.message });
  }
};

// Mock posts handler
const postsHandler = async (event) => {
  try {
    if (event.httpMethod === 'GET' && event.path === '/posts') {
      return createResponse(200, { posts: [], count: 0 });
    } else if (event.httpMethod === 'POST' && event.path === '/posts') {
      return createResponse(201, {
        message: 'Post created successfully',
        post: { id: 'test-post-id', title: 'Test Post' }
      });
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      return createResponse(200, { post: { id: event.pathParameters.id, title: 'Test Post' } });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error', details: error.message });
  }
};

// Mock media handler
const mediaHandler = async (event) => {
  try {
    if (event.httpMethod === 'POST' && event.path === '/media/upload') {
      return createResponse(200, {
        message: 'Upload URL generated successfully',
        mediaId: 'test-media-id',
        uploadUrl: 'https://test-url.com',
        media: { id: 'test-media-id', fileName: 'test.jpg' }
      });
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      return createResponse(200, { media: { id: event.pathParameters.id, fileName: 'test.jpg' } });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error', details: error.message });
  }
};

// Mock users handler
const usersHandler = async (event) => {
  try {
    if (event.httpMethod === 'GET' && event.path === '/users/profile') {
      return createResponse(200, {
        user: {
          id: 'test-user-id',
          username: 'testuser',
          bio: 'Test bio',
          followersCount: 0,
          followingCount: 0
        }
      });
    } else if (event.httpMethod === 'PUT' && event.path === '/users/profile') {
      return createResponse(200, { message: 'Profile updated successfully' });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error', details: error.message });
  }
};

module.exports = {
  healthHandler,
  authHandler,
  postsHandler,
  mediaHandler,
  usersHandler
};

/**
 * Unit tests for Users Lambda function
 */

const { TestDataGenerator, TEST_USERS } = require('../utils/test-data');
const { 
  resetAllMocks, 
  mockDynamoDBGet, 
  mockDynamoDBPut, 
  mockDynamoDBUpdate,
  mockDynamoDBDocumentClient
} = require('../utils/aws-mocks');

// Import the handler
const { handler } = require('../../src/users/index');

describe('Users Lambda Handler', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('GET /users/profile', () => {
    it('should return user profile successfully', async () => {
      const testUser = TEST_USERS.VALID_USER;
      const testProfile = TestDataGenerator.createUserProfile(testUser.id);

      // Mock user lookup
      mockDynamoDBDocumentClient.get
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({ Item: testUser })
        }))
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({ Item: testProfile })
        }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/profile',
        queryStringParameters: { userId: testUser.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.user).toBeDefined();
      expect(body.user.id).toBe(testUser.id);
      expect(body.user.email).toBe(testUser.email);
      expect(body.user.username).toBe(testUser.username);
      expect(body.user.bio).toBe(testProfile.bio);
      expect(body.user.followersCount).toBe(testProfile.followersCount);
      expect(body.user.followingCount).toBe(testProfile.followingCount);
    });

    it('should return 400 when userId is missing', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/profile',
        queryStringParameters: {}
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });

    it('should return 404 when user is not found', async () => {
      mockDynamoDBGet(null);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/profile',
        queryStringParameters: { userId: 'nonexistent-id' }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('User not found');
    });

    it('should return user with default profile values when profile not found', async () => {
      const testUser = TEST_USERS.VALID_USER;

      // Mock user found, but no profile
      mockDynamoDBDocumentClient.get
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({ Item: testUser })
        }))
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({}) // No profile item
        }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/profile',
        queryStringParameters: { userId: testUser.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.user.bio).toBe('');
      expect(body.user.followersCount).toBe(0);
      expect(body.user.followingCount).toBe(0);
      expect(body.user.postsCount).toBe(0);
    });
  });

  describe('PUT /users/profile', () => {
    it('should update user profile successfully', async () => {
      mockDynamoDBUpdate();

      const updateData = {
        userId: TEST_USERS.VALID_USER.id,
        firstName: 'Updated',
        lastName: 'Name',
        bio: 'Updated bio',
        location: 'Updated City',
        website: 'https://updated.example.com'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: '/users/profile',
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Profile updated successfully');
    });

    it('should return 400 when userId is missing', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: '/users/profile',
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });

    it('should update only provided fields', async () => {
      mockDynamoDBUpdate();

      const updateData = {
        userId: TEST_USERS.VALID_USER.id,
        bio: 'Only bio updated'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: '/users/profile',
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Profile updated successfully');
    });
  });

  describe('GET /users/{id}', () => {
    it('should return user by ID successfully', async () => {
      const testUser = TEST_USERS.VALID_USER;
      const testProfile = TestDataGenerator.createUserProfile(testUser.id);

      mockDynamoDBDocumentClient.get
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({ Item: testUser })
        }))
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({ Item: testProfile })
        }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: `/users/${testUser.id}`,
        pathParameters: { id: testUser.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.user).toBeDefined();
      expect(body.user.id).toBe(testUser.id);
      expect(body.user.username).toBe(testUser.username);
      // Should not include email for privacy
      expect(body.user.email).toBeUndefined();
    });

    it('should return 404 when user is not found', async () => {
      mockDynamoDBGet(null);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/nonexistent-id',
        pathParameters: { id: 'nonexistent-id' }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('User not found');
    });
  });

  describe('POST /users/{id}/follow', () => {
    it('should follow user successfully', async () => {
      // Mock that follow relationship doesn't exist
      mockDynamoDBGet(null);
      
      // Mock successful operations
      mockDynamoDBPut();
      mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const followData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/users/${TEST_USERS.ADMIN_USER.id}/follow`,
        pathParameters: { id: TEST_USERS.ADMIN_USER.id },
        body: JSON.stringify(followData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('User followed successfully');
    });

    it('should return 400 when userId is missing', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/users/${TEST_USERS.ADMIN_USER.id}/follow`,
        pathParameters: { id: TEST_USERS.ADMIN_USER.id },
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });

    it('should return 400 when trying to follow yourself', async () => {
      const followData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/users/${TEST_USERS.VALID_USER.id}/follow`,
        pathParameters: { id: TEST_USERS.VALID_USER.id },
        body: JSON.stringify(followData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Cannot follow yourself');
    });

    it('should return 400 when already following user', async () => {
      // Mock that follow relationship already exists
      mockDynamoDBGet({ 
        follower_id: TEST_USERS.VALID_USER.id, 
        following_id: TEST_USERS.ADMIN_USER.id 
      });

      const followData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/users/${TEST_USERS.ADMIN_USER.id}/follow`,
        pathParameters: { id: TEST_USERS.ADMIN_USER.id },
        body: JSON.stringify(followData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Already following this user');
    });
  });

  describe('DELETE /users/{id}/follow', () => {
    it('should unfollow user successfully', async () => {
      mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));
      
      mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const unfollowData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/users/${TEST_USERS.ADMIN_USER.id}/follow`,
        pathParameters: { id: TEST_USERS.ADMIN_USER.id },
        body: JSON.stringify(unfollowData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('User unfollowed successfully');
    });

    it('should return 400 when userId is missing', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/users/${TEST_USERS.ADMIN_USER.id}/follow`,
        pathParameters: { id: TEST_USERS.ADMIN_USER.id },
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for unknown endpoints', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Not found');
    });

    it('should include CORS headers in all responses', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.headers).toBeDefined();
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Headers']).toBeDefined();
      expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
    });
  });
});

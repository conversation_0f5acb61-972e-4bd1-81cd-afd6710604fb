# This file is auto generated by SAM CLI build command

[function_build_definitions.2b811085-41b5-423a-b432-00c1cf9ee1a1]
codeuri = "/media/justin/Projects/GameFlex/gameflex_mobile/backend/src/auth"
runtime = "nodejs20.x"
architecture = "x86_64"
handler = "index.handler"
manifest_hash = ""
packagetype = "Zip"
functions = ["AuthFunction"]

[function_build_definitions.34ce1f5b-aef5-4c58-bdb6-2763be694c9d]
codeuri = "/media/justin/Projects/GameFlex/gameflex_mobile/backend/src/posts"
runtime = "nodejs20.x"
architecture = "x86_64"
handler = "index.handler"
manifest_hash = ""
packagetype = "Zip"
functions = ["PostsFunction"]

[function_build_definitions.c1bcd7a3-5cf4-48af-8ae4-4a5871e555be]
codeuri = "/media/justin/Projects/GameFlex/gameflex_mobile/backend/src/media"
runtime = "nodejs20.x"
architecture = "x86_64"
handler = "index.handler"
manifest_hash = ""
packagetype = "Zip"
functions = ["MediaFunction"]

[function_build_definitions.171ac299-532f-46df-b66a-ba2a5fea0322]
codeuri = "/media/justin/Projects/GameFlex/gameflex_mobile/backend/src/users"
runtime = "nodejs20.x"
architecture = "x86_64"
handler = "index.handler"
manifest_hash = ""
packagetype = "Zip"
functions = ["UsersFunction"]

[function_build_definitions.ab246e09-c679-41b9-a70c-931a0d9b1830]
codeuri = "/media/justin/Projects/GameFlex/gameflex_mobile/backend/src/health"
runtime = "nodejs20.x"
architecture = "x86_64"
handler = "index.handler"
manifest_hash = ""
packagetype = "Zip"
functions = ["HealthFunction"]

[layer_build_definitions]

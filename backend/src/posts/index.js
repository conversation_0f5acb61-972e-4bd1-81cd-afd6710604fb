const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS SDK for SAM local
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);
const s3 = new AWS.S3(awsConfig);

const POSTS_TABLE = process.env.POSTS_TABLE;
const COMMENTS_TABLE = process.env.COMMENTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const MEDIA_BUCKET = process.env.MEDIA_BUCKET;

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Get all posts
const getPosts = async (event) => {
    try {
        const result = await dynamodb.scan({
            TableName: POSTS_TABLE
        }).promise();

        // Sort posts by created_at descending
        const posts = result.Items.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        return createResponse(200, {
            posts,
            count: posts.length
        });

    } catch (error) {
        console.error('GetPosts error:', error);
        return createResponse(500, { error: 'Failed to get posts', details: error.message });
    }
};

// Get single post
const getPost = async (event) => {
    try {
        const { id } = event.pathParameters;

        const result = await dynamodb.get({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        if (!result.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        return createResponse(200, { post: result.Item });

    } catch (error) {
        console.error('GetPost error:', error);
        return createResponse(500, { error: 'Failed to get post', details: error.message });
    }
};

// Create post
const createPost = async (event) => {
    try {
        const { title, content, mediaUrl, userId } = JSON.parse(event.body);

        if (!title || !content || !userId) {
            return createResponse(400, { error: 'Title, content, and userId are required' });
        }

        const postId = uuidv4();
        const post = {
            id: postId,
            title,
            content,
            mediaUrl: mediaUrl || null,
            userId,
            likes: 0,
            comments: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: POSTS_TABLE,
            Item: post
        }).promise();

        return createResponse(201, {
            message: 'Post created successfully',
            post
        });

    } catch (error) {
        console.error('CreatePost error:', error);
        return createResponse(500, { error: 'Failed to create post', details: error.message });
    }
};

// Update post
const updatePost = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { title, content, mediaUrl } = JSON.parse(event.body);

        const updateExpression = [];
        const expressionAttributeValues = {};
        const expressionAttributeNames = {};

        if (title) {
            updateExpression.push('#title = :title');
            expressionAttributeNames['#title'] = 'title';
            expressionAttributeValues[':title'] = title;
        }

        if (content) {
            updateExpression.push('#content = :content');
            expressionAttributeNames['#content'] = 'content';
            expressionAttributeValues[':content'] = content;
        }

        if (mediaUrl !== undefined) {
            updateExpression.push('mediaUrl = :mediaUrl');
            expressionAttributeValues[':mediaUrl'] = mediaUrl;
        }

        updateExpression.push('updated_at = :updated_at');
        expressionAttributeValues[':updated_at'] = new Date().toISOString();

        const result = await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: `SET ${updateExpression.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        }).promise();

        return createResponse(200, {
            message: 'Post updated successfully',
            post: result.Attributes
        });

    } catch (error) {
        console.error('UpdatePost error:', error);
        return createResponse(500, { error: 'Failed to update post', details: error.message });
    }
};

// Delete post
const deletePost = async (event) => {
    try {
        const { id } = event.pathParameters;

        await dynamodb.delete({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        return createResponse(200, { message: 'Post deleted successfully' });

    } catch (error) {
        console.error('DeletePost error:', error);
        return createResponse(500, { error: 'Failed to delete post', details: error.message });
    }
};

// Like post
const likePost = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { userId } = JSON.parse(event.body);

        if (!userId) {
            return createResponse(400, { error: 'userId is required' });
        }

        // Check if already liked
        const existingLike = await dynamodb.get({
            TableName: LIKES_TABLE,
            Key: { post_id: id, user_id: userId }
        }).promise();

        if (existingLike.Item) {
            return createResponse(400, { error: 'Post already liked' });
        }

        // Add like
        await dynamodb.put({
            TableName: LIKES_TABLE,
            Item: {
                post_id: id,
                user_id: userId,
                created_at: new Date().toISOString()
            }
        }).promise();

        // Update post likes count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        }).promise();

        return createResponse(200, { message: 'Post liked successfully' });

    } catch (error) {
        console.error('LikePost error:', error);
        return createResponse(500, { error: 'Failed to like post', details: error.message });
    }
};

// Unlike post
const unlikePost = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { userId } = JSON.parse(event.body);

        if (!userId) {
            return createResponse(400, { error: 'userId is required' });
        }

        // Remove like
        await dynamodb.delete({
            TableName: LIKES_TABLE,
            Key: { post_id: id, user_id: userId }
        }).promise();

        // Update post likes count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        }).promise();

        return createResponse(200, { message: 'Post unliked successfully' });

    } catch (error) {
        console.error('UnlikePost error:', error);
        return createResponse(500, { error: 'Failed to unlike post', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'GET' && path === '/posts') {
            return await getPosts(event);
        } else if (httpMethod === 'POST' && path === '/posts') {
            return await createPost(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id) {
            return await updatePost(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id) {
            return await deletePost(event);
        } else if (httpMethod === 'POST' && path.includes('/like')) {
            return await likePost(event);
        } else if (httpMethod === 'DELETE' && path.includes('/like')) {
            return await unlikePost(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};

const AWS = require('aws-sdk');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

// Configure AWS SDK for SAM local
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

// For local development with SAM, we need to set the endpoint if AWS_SAM_LOCAL is set
if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognito = new AWS.CognitoIdentityServiceProvider(awsConfig);
const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);

const USER_POOL_ID = process.env.USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE;

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Sign up function
const signUp = async (event) => {
    try {
        const { email, password, username, firstName, lastName } = JSON.parse(event.body);

        if (!email || !password || !username) {
            return createResponse(400, { error: 'Email, password, and username are required' });
        }

        // Create user in Cognito
        const cognitoParams = {
            UserPoolId: USER_POOL_ID,
            Username: email,
            TemporaryPassword: password,
            MessageAction: 'SUPPRESS',
            UserAttributes: [
                { Name: 'email', Value: email },
                { Name: 'email_verified', Value: 'true' },
                { Name: 'given_name', Value: firstName || '' },
                { Name: 'family_name', Value: lastName || '' }
            ]
        };

        const cognitoUser = await cognito.adminCreateUser(cognitoParams).promise();

        // Set permanent password
        await cognito.adminSetUserPassword({
            UserPoolId: USER_POOL_ID,
            Username: email,
            Password: password,
            Permanent: true
        }).promise();

        // Create user record in DynamoDB
        const userId = uuidv4();
        const userRecord = {
            id: userId,
            email,
            username,
            firstName: firstName || '',
            lastName: lastName || '',
            cognito_user_id: cognitoUser.User.Username,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: USERS_TABLE,
            Item: userRecord
        }).promise();

        return createResponse(201, {
            message: 'User created successfully',
            user: {
                id: userId,
                email,
                username,
                firstName: firstName || '',
                lastName: lastName || ''
            }
        });

    } catch (error) {
        console.error('SignUp error:', error);
        return createResponse(500, { error: 'Failed to create user', details: error.message });
    }
};

// Sign in function
const signIn = async (event) => {
    try {
        const { email, password } = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        // Authenticate with Cognito
        const authParams = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'ADMIN_NO_SRP_AUTH',
            AuthParameters: {
                USERNAME: email,
                PASSWORD: password
            }
        };

        const authResult = await cognito.adminInitiateAuth(authParams).promise();

        // Get user details from DynamoDB
        const userResult = await dynamodb.query({
            TableName: USERS_TABLE,
            IndexName: 'EmailIndex',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email
            }
        }).promise();

        if (userResult.Items.length === 0) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Items[0];

        return createResponse(200, {
            message: 'Sign in successful',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                refreshToken: authResult.AuthenticationResult.RefreshToken,
                idToken: authResult.AuthenticationResult.IdToken
            },
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
            }
        });

    } catch (error) {
        console.error('SignIn error:', error);
        return createResponse(401, { error: 'Authentication failed', details: error.message });
    }
};

// Refresh token function
const refreshToken = async (event) => {
    try {
        const { refreshToken } = JSON.parse(event.body);

        if (!refreshToken) {
            return createResponse(400, { error: 'Refresh token is required' });
        }

        const refreshParams = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'REFRESH_TOKEN_AUTH',
            AuthParameters: {
                REFRESH_TOKEN: refreshToken
            }
        };

        const authResult = await cognito.adminInitiateAuth(refreshParams).promise();

        return createResponse(200, {
            message: 'Token refreshed successfully',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                idToken: authResult.AuthenticationResult.IdToken
            }
        });

    } catch (error) {
        console.error('RefreshToken error:', error);
        return createResponse(401, { error: 'Token refresh failed', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    try {
        switch (`${httpMethod} ${path}`) {
            case 'POST /auth/signup':
                return await signUp(event);
            case 'POST /auth/signin':
                return await signIn(event);
            case 'POST /auth/refresh':
                return await refreshToken(event);
            default:
                return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
